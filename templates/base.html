<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CozyWish - Book Beauty & Wellness Services{% endblock %}</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- External CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    {% load static %}
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    <link href="{% static 'css/base.css' %}" rel="stylesheet">
    <link href="{% static 'css/navbar.css' %}" rel="stylesheet">

    <!-- Additional head content -->
    {% block extra_head %}{% endblock %}
    {% block extra_css %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}" data-authenticated="{% if user.is_authenticated %}true{% else %}false{% endif %}">
    <!-- Navigation Bar -->
    {% include 'includes/navbar_cw.html' %}

    {% if hero_section %}
        <!-- Hero page content with gradient background -->
        <div class="radial-gradient">
            <!-- Hero section content -->
            {% block hero_content %}{% endblock %}
        </div>
    {% endif %}


    <!-- System Messages -->
    {% if messages %}
        <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1100;">
            {% for message in messages %}
                <div class="toast text-bg-{{ message.tags }} mb-2" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">{{ message }}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content Area -->
    <main class="container py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Site Footer -->
    <footer class="py-4 mt-5" style="background-color: white; border-top: 2px solid black;">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5 style="color: black;">CozyWish</h5>
                    <p style="color: black; opacity: 0.7;">Find & Book Local Spa and Massage Services</p>
                </div>
                <div class="col-md-4">
                    <h5 style="color: black;">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'home' %}" class="text-decoration-none {% if request.resolver_match.url_name == 'home' %}active{% endif %}" style="color: black;">Home</a></li>
                        <li><a href="{% url 'venues_app:venue_list' %}" class="text-decoration-none {% if request.resolver_match.url_name == 'venue_list' or request.resolver_match.url_name == 'venue_detail' %}active{% endif %}" style="color: black;">Venues</a></li>
                        <li><a href="{% url 'discount_app:featured_discounts' %}" class="text-decoration-none {% if request.resolver_match.url_name == 'featured_discounts' %}active{% endif %}" style="color: black;">Discounts</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5 style="color: black;">Contact Us</h5>
                    <address style="color: black; opacity: 0.7;">
                        <p><i class="fas fa-map-marker-alt me-2"></i> 123 Main St, New York, NY 10001</p>
                        <p><i class="fas fa-phone me-2"></i> (*************</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr style="border-color: black;">
            <div class="text-center">
                <p style="color: black; opacity: 0.7;" class="mb-0">&copy; {% now "Y" %} CozyWish. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN" crossorigin="anonymous"></script>

    <!-- Custom JavaScript Files -->
    <script src="{% static 'js/messages.js' %}"></script>
    <script src="{% static 'js/discounts.js' %}"></script>
    <script src="{% static 'js/payments.js' %}"></script>
    <script src="{% static 'js/form_loading.js' %}"></script>
    <script src="{% static 'js/notifications.js' %}"></script>

    <!-- Bootstrap Tooltip Initialization -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
        });
    </script>

    <!-- Additional JavaScript -->
    {% block extra_js %}{% endblock %}
</body>
</html>